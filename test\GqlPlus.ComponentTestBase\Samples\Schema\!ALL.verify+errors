!!! I@025/0153 : Invalid Input Alternate. Type kind mismatch for RefCnstAltObjInp. Found Input 'AltCnstAltObjInp'. - 'RefCnstAltObjInp' !!!
!!! I@026/0138 : Invalid Input Alternate. Type kind mismatch for RefCnstAltDualInp. Found Input 'AltCnstAltDualInp'. - 'RefCnstAltDualInp' !!!
!!! I@026/0195 : Invalid Input Parent. Type kind mismatch for RefCnstFieldObjInp. Found Input 'AltCnstFieldObjInp'. - 'RefCnstFieldObjInp' !!!
!!! I@027/0180 : Invalid Input Parent. Type kind mismatch for RefCnstFieldDualInp. Found Input 'AltCnstFieldDualInp'. - 'RefCnstFieldDualInp' !!!
!!! I@027/0365 : Invalid Input Alternate. Type kind mismatch for RefGnrcAltParamInp. Found Input 'AltGnrcAltParamInp'. - 'RefGnrcAltParamInp' !!!
!!! I@027/0521 : Invalid Input Parent. Type kind mismatch for RefGnrcPrntParamInp. Found Input 'AltGnrcPrntParamInp'. - 'RefGnrcPrntParamInp' !!!
!!! I@029/0243 : Invalid Input Parent. Type kind mismatch for RefCnstPrntObjPrntInp. Found Input 'AltCnstPrntObjPrntInp'. - 'RefCnstPrntObjPrntInp' !!!
!!! I@030/0211 : Invalid Input Parent. Type kind mismatch for RefCnstPrntDualGrndInp. Found Input 'AltCnstPrntDualGrndInp'. - 'RefCnstPrntDualGrndInp' !!!
!!! I@030/0228 : Invalid Input Parent. Type kind mismatch for RefCnstPrntDualPrntInp. Found Input 'AltCnstPrntDualPrntInp'. - 'RefCnstPrntDualPrntInp' !!!
!!! I@031/0533 : Invalid Input Parent. Type kind mismatch for RefGnrcPrntParamPrntInp. Found Input 'AltGnrcPrntParamPrntInp'. - 'RefGnrcPrntParamPrntInp' !!!
!!! I@034/0419 : Invalid Input Field. Type kind mismatch for RefGnrcFieldParamInp. Found Input 'AltGnrcFieldParamInp'. - 'RefGnrcFieldParamInp' !!!
